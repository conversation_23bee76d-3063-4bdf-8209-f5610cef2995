<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
  <head>
    <title>JQVMap - Germany Map</title>
    
    <link href="../jqvmap/jqvmap.css" media="screen" rel="stylesheet" type="text/css" />
    
    <script src="http://ajax.googleapis.com/ajax/libs/jquery/1.7.2/jquery.min.js" type="text/javascript"></script>
    <script src="../jqvmap/jquery.vmap.js" type="text/javascript"></script>
    <script src="../jqvmap/maps/jquery.vmap.germany.js" type="text/javascript"></script>
    
	<script type="text/javascript">
	jQuery(document).ready(function() {
		jQuery('#vmap').vectorMap({
		    map: 'germany_en',
		    onRegionClick: function(element, code, region)
		    {
		        var message = 'You clicked "'
		            + region 
		            + '" which has the code: '
		            + code.toUpperCase();
             
		        alert(message);
		    }
		});
	});
	</script>
  </head>
  <body>
    <div id="vmap" style="width: 400px; height: 400px;"></div>
  </body>
</html>
