<?php
//ini_set("soap.wsdl_cache_enabled", "0");

// Canada Post credentials
$username = 'b852cbebf491d513';
$password = '7516a83437a13f4191cd18';
$customerNumber = '8969616';

// The shipment ID you got when creating the shipment
$shipmentId = '8368668320795609'; // replace with your actual shipment ID



// Canada Post API credentials
$username = 'b852cbebf491d513';       // Replace with your API username
$password = '7516a83437a13f4191cd18';       // Replace with your API password
$customerNumber = '8969616';     // Replace with your 10-digit customer number
$shipmentId = '8368668320795609';           // Replace with the actual shipment ID

// Use sandbox or production URL
$endpoint = "https://soa-gw.canadapost.ca/rs/" . $customerNumber . "/" . $shipmentId;

// Initialize cURL
$ch = curl_init($endpoint);

curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_USERPWD, "$username:$password");
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    'Accept: application/vnd.cpc.shipment-v8+xml',
));
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);

// Execute API call
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

if (curl_errno($ch)) {
    echo 'cURL error: ' . curl_error($ch);
    curl_close($ch);
    exit;
}

curl_close($ch);

if ($httpCode == 200) {
    // Load the XML response
    $xml = simplexml_load_string($response);

    // Extract pricing info
    $shipmentCost = $xml->{'shipment-info'}->{'shipment-cost'};
    $base = (string)$shipmentCost->base;
    $gst = (string)$shipmentCost->gst;
    $hst = (string)$shipmentCost->hst;
    $pst = (string)$shipmentCost->pst;
    $due = (string)$shipmentCost->due;

    echo "Shipment ID: $shipmentId\n";
    echo "Base: \$$base\n";
    echo "GST: \$$gst\n";
    echo "HST: \$$hst\n";
    echo "PST: \$$pst\n";
    echo "Total Due: \$$due\n";

} else {
    echo "Error: HTTP $httpCode\n";
    echo "Response:\n$response\n";
}

?>
