<?php
 /**
 * Sample code for the GetNearestPostOffice Canada Post service.
 * 
 * The GetNearestPostOffice service returns a list of Post Offices 
 * near a given location including information such as address and phone number.
 *
 * This sample is configured to access the Developer Program sandbox environment. 
 * Use your development key username and password for the web service credentials.
 * 
 **/

// Your username and password are imported from the following file
// CPCWS_SOAP_PostOffice_PHP_Samples\SOAP\postoffice\user.ini
$userProperties = parse_ini_file(realpath(dirname($_SERVER['SCRIPT_FILENAME'])) . '/../user.ini');

$wsdl = realpath(dirname($_SERVER['SCRIPT_FILENAME'])) . '/../../wsdl/postoffice.wsdl';

$hostName = 'ct.soa-gw.canadapost.ca';

// SOAP URI
$location = 'https://' . $hostName . '/rs/soap/postoffice';

// SSL Options
$opts = array('ssl' =>
	array(
		'verify_peer'=> false,
		'cafile' => realpath(dirname($_SERVER['SCRIPT_FILENAME'])) . '/../../../third-party/cert/cacert.pem',
		'CN_match' => $hostName
	)
);

$ctx = stream_context_create($opts);	
$client = new SoapClient($wsdl,array('location' => $location, 'features' => SOAP_SINGLE_ELEMENT_ARRAYS, 'stream_context' => $ctx));

// Set WS Security UsernameToken
$WSSENS = 'http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd';
$usernameToken = new stdClass(); 
$usernameToken->Username = new SoapVar($userProperties['username'], XSD_STRING, null, null, null, $WSSENS);
$usernameToken->Password = new SoapVar($userProperties['password'], XSD_STRING, null, null, null, $WSSENS);
$content = new stdClass(); 
$content->UsernameToken = new SoapVar($usernameToken, SOAP_ENC_OBJECT, null, null, null, $WSSENS);
$header = new SOAPHeader($WSSENS, 'Security', $content);
$client->__setSoapHeaders($header); 

try {
	// Execute Request
	$result = $client->__soapCall('GetNearestPostOffice', array(
	    'get-nearest-post-office-request' => array(
			'locale'	=> 'EN',
			'maximum'	=> '4',
			'search-data'	=> array(
				// 'city'			=> 'Ottawa',
				'postal-code'	=> 'K2B8J6'
				// 'province'		=> 'ON',
				// 'street-name'	=> 'Richmond'	
			)
		)
	), NULL, NULL);
	
	// Parse Response
	if ( isset($result->{'post-office-list'}) ) {
		foreach ( $result->{'post-office-list'}->{'post-office'} as $postOffice ) {
			echo 'Office Name: ' . $postOffice->{'name'} . "\n";
			echo 'Office Id: ' . $postOffice->{'office-id'} . "\n";
			echo 'Address: ' . $postOffice->{'address'}->{'office-address'} . "\n\n";
		}
	} else {
		foreach ( $result->{'messages'}->{'message'} as $message ) {
			echo 'Error Code: ' . $message->code . "\n";
			echo 'Error Msg: ' . $message->description . "\n\n";
		}
	}
} catch (SoapFault $exception) {
	echo 'Fault Code: ' . trim($exception->faultcode) . "\n"; 
	echo 'Fault Reason: ' . trim($exception->getMessage()) . "\n"; 
}

?>