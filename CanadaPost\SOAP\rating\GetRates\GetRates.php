<?php
 /**
 * Sample code for the GetRates Canada Post service.
 * 
 * The GetRates service returns a list of shipping services, prices and transit times 
 * for a given item to be shipped. 
 *
 * This sample is configured to access the Developer Program sandbox environment. 
 * Use your development key username and password for the web service credentials.
 * 
 **/

// Your username and password are imported from the following file
// CPCWS_SOAP_Rating_PHP_Samples\SOAP\rating\user.ini
$userProperties = parse_ini_file(realpath(dirname($_SERVER['SCRIPT_FILENAME'])) . '/../user.ini');

$wsdl = realpath(dirname($_SERVER['SCRIPT_FILENAME'])) . '/../../wsdl/rating.wsdl';

$hostName = 'ct.soa-gw.canadapost.ca';

// SOAP URI
$location = 'https://' . $hostName . '/rs/soap/rating/v4';

// SSL Options
$opts = array('ssl' =>
	array(
		'verify_peer'=> false,
		'cafile' => realpath(dirname($_SERVER['SCRIPT_FILENAME'])) . '/../../../third-party/cert/cacert.pem',
		'CN_match' => $hostName
	)
);

$ctx = stream_context_create($opts);	
$client = new SoapClient($wsdl,array('location' => $location, 'features' => SOAP_SINGLE_ELEMENT_ARRAYS, 'stream_context' => $ctx));

// Set WS Security UsernameToken
$WSSENS = 'http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd';
$usernameToken = new stdClass(); 
$usernameToken->Username = new SoapVar($userProperties['username'], XSD_STRING, null, null, null, $WSSENS);
$usernameToken->Password = new SoapVar($userProperties['password'], XSD_STRING, null, null, null, $WSSENS);
$content = new stdClass(); 
$content->UsernameToken = new SoapVar($usernameToken, SOAP_ENC_OBJECT, null, null, null, $WSSENS);
$header = new SOAPHeader($WSSENS, 'Security', $content);
$client->__setSoapHeaders($header); 

try {
	// Execute Request
	$mailedBy = $userProperties['customerNumber'];
	$originPostalCode = 'Z2B1A0'; 
	$postalCode = 'K1K4T3';
	$weight = 1;

	$result = $client->__soapCall('GetRates', array(
	    'get-rates-request' => array(
			'locale'			=> 'EN',
			'mailing-scenario' 			=> array(
				'customer-number'			=> $mailedBy,
				'parcel-characteristics'	=> array(
					'weight'					=> $weight
				),
				'origin-postal-code'		=> $originPostalCode,
				'destination' 			=> array(
					'domestic' 				=> array(
						'postal-code'			=> $postalCode
					)
				)
			)
		)
	), NULL, NULL);
	
	// Parse Response
	
	if ( isset($result->{'price-quotes'}) ) {
		foreach ( $result->{'price-quotes'}->{'price-quote'} as $priceQuote ) {  
			echo 'Service Name: ' . $priceQuote->{'service-name'} . "\n";
			echo 'Price: ' . $priceQuote->{'price-details'}->{'due'} . "\n\n";	
		}
	} else {
		foreach ( $result->{'messages'}->{'message'} as $message ) {
			echo 'Error Code: ' . $message->code . "\n";
			echo 'Error Msg: ' . $message->description . "\n\n";
		}
	}
	
} catch (SoapFault $exception) {
	echo 'Fault Code: ' . trim($exception->faultcode) . "\n"; 
	echo 'Fault Reason: ' . trim($exception->getMessage()) . "\n"; 
}

?>

