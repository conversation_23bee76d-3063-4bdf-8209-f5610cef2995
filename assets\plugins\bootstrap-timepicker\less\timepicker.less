/*!
 * Timepicker for Bootstrap
 *
 * Copyright 2012 <PERSON><PERSON>, <PERSON>, <PERSON>
 * Licensed under the Apache License v2.0
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 */
.bootstrap-timepicker {
    &.dropdown-menu {
        top: 0;
        left: 0;
        padding: 4px;
        margin-top: 1px;
        -webkit-border-radius: 4px;
        -moz-border-radius: 4px;
        border-radius: 4px;
        display: none;
        min-width: 10px;
        z-index: 99999;

        &.open {
            display: inline-block;
        }

        &:before {
            content: '';
            border-left: 7px solid transparent;
            border-right: 7px solid transparent;
            border-bottom: 7px solid #ccc;
            border-bottom-color: rgba(0, 0, 0, 0.2);
            position: absolute;
            top: -7px;
            left: 6px;
        }

        &:after {
            content: '';
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-bottom: 6px solid #ffffff;
            position: absolute;
            top: -6px;
            left: 7px;
        }
    }

    &.modal {
        top: 30%; 
        margin-top: 0; 
        width: 200px; 
        margin-left: -100px; 

        .modal-content {
            padding: 0;
        }
    }

    table {
        width: 100%;
        margin: 0;

        td { 
            text-align: center;
            height: 30px;
            margin: 0;
            padding: 2px;
        }

        td {
            span {
                width: 100%;
            }
            a {
                border: 1px transparent solid;
                width: 3em;
                display: inline-block;
                margin: 0;
                padding: 8px 0;
                outline: 0;
                color: #333;

                &:hover {
                    text-decoration: none;
                    background-color: #eee;
                    -webkit-border-radius: 4px;
                    -moz-border-radius: 4px;
                    border-radius: 4px;
                    border-color: #ddd;
                }

                i {
                    margin-top: 2px;
                }
            }
            input {
                width: 25px;
                margin: 0;
                text-align: center;
            }

        }
    }
}
.bootstrap-timepicker-component {
    .add-on { 
        cursor: pointer;
        i {
           display: block;
           width: 16px;
           height: 16px;
        }
    }
}

