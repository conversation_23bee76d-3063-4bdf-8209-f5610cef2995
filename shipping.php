<?php

// Canada Post OAuth token endpoint
$authUrl = 'https://ct.soa-gw.canadapost.ca/oauth2/token';

// Canada Post API credentials
$clientId = 'b852cbebf491d513'; // Development API Username
$clientSecret = '7516a83437a13f4191cd18'; // Development API Password/Key

// Step 1: Authenticate and Get OAuth Token
function getAccessToken($authUrl, $clientId, $clientSecret) {
    $authCredentials = base64_encode("$clientId:$clientSecret");

    $ch = curl_init($authUrl);
    /*curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Authorization: Basic $authCredentials",
        "Content-Type: application/x-www-form-urlencoded"
    ]);
    curl_setopt($ch, CURLOPT_POSTFIELDS, 'grant_type=client_credentials');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true); // Include headers in the output to inspect the HTTP status code
    curl_setopt($ch, CURLOPT_VERBOSE, true); // Enable verbose output for debugging
    */
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Authorization: Basic $authCredentials",
        "Content-Type: application/x-www-form-urlencoded"
    ]);
    curl_setopt($ch, CURLOPT_POSTFIELDS, 'grant_type=client_credentials');
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    //curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
    //curl_setopt($ch, CURLOPT_CAINFO, realpath(dirname($_SERVER['SCRIPT_FILENAME'])) . '/../../../third-party/cert/cacert.pem'); // Signer Certificate in PEM format
    //curl_setopt($ch, CURLOPT_POST, true);
    //curl_setopt($ch, CURLOPT_POSTFIELDS, $xmlRequest);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($ch, CURLOPT_USERPWD, $clientId . ':' . $clientSecret);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/vnd.cpc.shipment-v8+xml', 'Accept: application/vnd.cpc.shipment-v8+xml'));


    $response = curl_exec($ch);
    $httpStatusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    // Debugging output
    echo "HTTP Status Code: $httpStatusCode\n";
    echo "cURL Error (if any): $error\n";
    echo "Full Response:\n";
    print_r($response);

    // Parse the JSON response
    $tokenData = json_decode($response, true);

    return $tokenData['access_token'] ?? null;
}

// Execute the OAuth function
$accessToken = getAccessToken($authUrl, $clientId, $clientSecret);
if ($accessToken) {
    echo "Access Token: $accessToken";
} else {
    echo "Error: Failed to obtain access token.";
}

?>
