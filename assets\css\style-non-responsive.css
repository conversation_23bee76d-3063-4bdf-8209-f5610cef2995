/***
Large notebooks and desktops
***/

 	/***
 	Page sidebar
 	***/
 	.page-sidebar {
		position: absolute;
 		width: 225px;
  	}

	.page-sidebar-fixed .page-sidebar {
	  	position: fixed !important;
	 	top: 41px;
	}

	.page-sidebar-fixed ul.page-sidebar-menu > li.last {
		margin-bottom: 15px !important;
	}

	.page-sidebar-fixed.page-sidebar-hover-on .page-sidebar {
		z-index: 10000;		
		width: 35px;		
	}

	.page-sidebar-fixed.page-sidebar-hover-on .page-sidebar .selected {
		display: none;
	}

	.page-sidebar-fixed.page-sidebar-hover-on .page-content {
		margin-left: 35px;
	}
	.page-sidebar-fixed.page-sidebar-hover-on .footer {
		margin-left: 35px;
	}

	.page-sidebar-fixed .page-sidebar-closed .page-sidebar .sidebar-search .submit,
	.page-sidebar-fixed .page-sidebar .sidebar-toggler {
		-webkit-transition: all  0.2s ease;
	     -moz-transition: all  0.2s ease;
	       -o-transition: all  0.2s ease;
	          transition: all  0.2s ease;
	}

	.page-sidebar-hovering {
		overflow: hidden !important;
	}

	.page-sidebar-hovering .sub-menu,
	.page-sidebar-hovering span.title,
	.page-sidebar-hovering span.arrow {		  
		display: none !important;
	}

	.page-sidebar-hovering .submit {
		opacity: 0;
		width: 0 !important;
		height: 0 !important;
	}

  	/***
  	Page content
  	***/
  	.page-content { 
  		margin-left: 225px; 
		margin-top: 0px;
		min-height: 760px; 
	}

	.page-sidebar-fixed .page-content {
		min-height: 600px; 
	}

	.page-content.no-min-height {
		min-height: auto;
	}

	/***
	Footer
	***/

	/* fixed sidebar */
	.page-sidebar-fixed .footer {
	  margin-left: 225px;
	  background-color: #fff;
	}

	.page-sidebar-fixed .footer .footer-inner {
	  color: #333;
	}

	.page-sidebar-fixed.page-sidebar-closed .footer {
	  margin-left: 35px;
	}

	.page-sidebar-fixed .footer .footer-tools .go-top { 
	  background-color: #666;
	}

	.page-sidebar-fixed .footer .footer-tools .go-top i {
	  color: #ddd;
	}

	/* boxed layout */
	.page-boxed	.header .brand {
  		margin-left: 0px !important;
  		width: 226px;
	}

	.page-boxed .header .brand img {
		margin-left: 10px;
	}
