<?php
if(isset($_GET['order_id']))
{
    $orderID = $_GET['order_id'];
}

include('includes/constant.php');
//require_once __DIR__ . '/../wp-load.php';	
// Database connection settings
$host       = HOSTDB;//'127.0.0.1:3306';
$username   = USERNAME;//'monarchb';
$password   = PASSWORD;//'Ist0vSesFjldu01';//'';
$dbname     = DATABASE;//'monarchb';

// Create a connection
$conn = new mysqli($host, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

function parseTrackingSummary($xml) {
    $pinSummary = $xml->{'pin-summary'};
    return [
        'Event Description' => (string) $pinSummary->{'event-description'},
    ];
}

function sendCustomerEmail($DB,$orderID)
{
    //$DB    = new DB_connection();
    
    if(is_numeric($orderID) && $orderID != 0)
    {
        require_once __DIR__ . '/../wp-load.php';
        $query	= "SELECT order_id, b_email, s_customer, tracking_event_description FROM `inv_woo_orders` WHERE `order_id` = " . $orderID;
        $conn	= $DB->query($query);
        if(mysqli_num_rows($conn) > 0)
        {
            $fetch = mysqli_fetch_object($conn);        
            
            $order_id       = $fetch->order_id;
            $email          = $fetch->b_email;
            $name           = $fetch->s_customer;
            $delivery_note  = $fetch->tracking_event_description;
            //$recipients = ['<EMAIL>', $email];
            $recipients = ['<EMAIL>', $email];
            // Now use wp_mail() as WP Mail SMTP will hook into it
            $subject = "Your Order #" . $order_id . " Has Been Delivered - Thank You for Shopping with Us!";
            $message = '<div dir="ltr">
                <div class="adM">
                <br clear="all">
                </div>
                <div>
                <div dir="ltr" class="gmail_signature" data-smartmail="gmail_signature">
                    <div dir="ltr">
                    <span style="color:rgb(32,31,30);font-size:15px">
                        <h4>Hello ' . $name . ',</h4>

                        <p dir="ltr" style="margin-top:0pt;margin-bottom:10pt;line-height:1.656">Delivery Note: <strong>' . $delivery_note . '</strong></p>

                        <p dir="ltr" style="margin-top:0pt;margin-bottom:10pt;line-height:1.656">We’re happy to let you know that your order <strong>#' . $order_id . '</strong> has been successfully delivered.</p>

                        <p dir="ltr" style="margin-top:0pt;margin-bottom:10pt;line-height:1.656">We hope everything arrived in perfect condition and that you’re satisfied with your purchase.</p>

                        <p dir="ltr" style="margin-top:0pt;margin-bottom:10pt;line-height:1.656">If you have any questions or need assistance, feel free to reach out to our support team at any time.</p>

                        <p dir="ltr" style="margin-top:0pt;margin-bottom:10pt;line-height:1.656">Thank you for choosing <strong>Monarch Butterflies</strong>!</p>

                        <p dir="ltr" style="margin-top:0pt;margin-bottom:0pt;line-height:1.656">
                        <span style="margin:0px;font-size:12pt;line-height:normal;color:rgb(34,34,34)!important">
                            <img src="https://ci3.googleusercontent.com/meips/ADKq_Nb4nqWA7SCpMuoLoLVkEHt0RVJWx6njTmbLLb3DIBy0l_SfbRb7H4P5Z_U3Sl4mSrUm3CvKKxQva4KvpGQg9oEPIgB6IAxK6FP_O8Cu6RRM1NjQ1Sdk6J3oTPLjKGOYWVjImA=s0-d-e1-ft#https://monarchbutterflies.ca/wp-content/uploads/2023/03/Outlook-iohnd4ci.jpg" width="420" height="77" class="CToWUd" data-bit="iit">
                            <br>
                            <br>
                            <img src="https://ci3.googleusercontent.com/meips/ADKq_NYtUWiFDhGv5vN-TocMP370exmpS0PYpDhSlvuG1l7GvaoiNn9giKLUQ4YGYo40j8OOJbAkruwigNND6rOscuZ3qJqZPexzgJL8GqRAFQBKpTm6cn6YTOIIrJSWTOnN7TnjVw=s0-d-e1-ft#https://monarchbutterflies.ca/wp-content/uploads/2023/03/Outlook-qyu1r5m4.png" width="200" height="41" class="CToWUd" data-bit="iit">
                            <br>
                        </span>
                        </p>
                    </span>
                    <span style="color:rgb(32,31,30);font-size:15px"></span>
                    <div style="margin:0px;font-size:12pt;font-family:Calibri,Arial,Helvetica,sans-serif">
                        <div dir="ltr" style="color:rgb(0,0,0)!important;margin:0px;line-height:1.656">
                        <span style="font-size:12pt;margin:0px;line-height:normal;color:rgb(34,34,34)!important">Tel: <span style="margin:0px">&nbsp;</span>
                        </span>
                        <span style="font-size:12pt;margin:0px;font-weight:700;line-height:normal;color:rgb(230,145,56)!important">1 8444 FLYFLY (359359)</span>
                        <br>
                        </div>
                        <p dir="ltr" style="margin-top:0pt;margin-bottom:0pt;line-height:1.656">
                        <span style="color:rgb(34,34,34)!important;margin:0px;line-height:normal">Email: <span style="margin:0px">&nbsp;</span>
                        </span>
                        <span style="margin:0px;font-weight:700;line-height:normal">
                            <a href="mailto:<EMAIL>" target="_blank">
                            <font color="#e69138">info@ <wbr>monarchbutterflies.ca </font>
                            </a>
                        </span>
                        </p>
                        <p dir="ltr" style="color:rgb(0,0,0)!important;margin-top:0pt;margin-bottom:0pt;line-height:1.656">
                        <span style="margin:0px;line-height:normal;color:rgb(34,34,34)!important">Website: <span style="margin:0px">&nbsp;</span>
                        </span>
                        <a href="http://monarchbutterflies.ca/" rel="noopener noreferrer" style="color:rgb(17,85,204);margin:0px" target="_blank" data-saferedirecturl="https://www.google.com/url?q=http://monarchbutterflies.ca/&amp;source=gmail&amp;ust=1746610312246000&amp;usg=AOvVaw0VNSSWuu9zs8LXWW4rPw-s">
                            <span style="margin:0px;font-weight:700;line-height:normal;color:rgb(230,145,56)!important">MonarchButterflies.ca</span>
                        </a>
                        </p>
                        <p dir="ltr" style="color:rgb(0,0,0)!important;margin-top:0pt;margin-bottom:0pt;line-height:1.656">
                        <span style="margin:0px;font-size:11pt;color:rgb(34,34,34)!important">
                            <span style="margin:0px;border:none;display:inline-block;overflow:hidden;width:31px;height:31px">
                            <a href="https://www.facebook.com/monarchbutterflies.ca" rel="noopener noreferrer" title="https://www.facebook.com/monarchbutterflies.ca" style="color:rgb(17,85,204);margin:0px" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://www.facebook.com/monarchbutterflies.ca&amp;source=gmail&amp;ust=1746610312246000&amp;usg=AOvVaw1hqiDY0LHr2NXSscxZgY-5">
                                <img alt="facebook" width="31" height="31" src="https://monarchbutterflies.ca/reporting/assets/img/facebook.png" style="margin:0px;width:31px;height:31px" crossorigin="" class="CToWUd" data-bit="iit">
                            </a>
                            </span>
                        </span>
                        <span style="margin:0px;font-size:11pt;color:rgb(34,34,34)!important">&nbsp;</span>
                        <span style="margin:0px;font-size:11pt;color:rgb(34,34,34)!important">
                            <span style="margin:0px;border:none;display:inline-block;overflow:hidden;width:31px;height:31px">
                            <a href="https://livemonarchs.tumblr.com/" rel="noopener noreferrer" title="https://livemonarchs.tumblr.com/" style="color:rgb(17,85,204);margin:0px" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://livemonarchs.tumblr.com/&amp;source=gmail&amp;ust=1746610312246000&amp;usg=AOvVaw3tGsJ-m-Uom-IBoeS5l8fm">
                                <img alt="tumblr" width="31" height="31" src="https://monarchbutterflies.ca/reporting/assets/img/tumblr.png" style="margin:0px;width:31px;height:31px" crossorigin="" class="CToWUd" data-bit="iit">
                            </a>
                            </span>
                        </span>
                        <span style="margin:0px;font-size:11pt;color:rgb(34,34,34)!important">&nbsp;</span>
                        <span style="margin:0px;font-size:11pt;color:rgb(34,34,34)!important">
                            <span style="margin:0px;border:none;display:inline-block;overflow:hidden;width:31px;height:31px">
                            <a href="https://www.instagram.com/monarchbutterflies.ca/" rel="noopener noreferrer" title="https://www.instagram.com/monarchbutterflies.ca/" style="color:rgb(17,85,204);margin:0px" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://www.instagram.com/monarchbutterflies.ca/&amp;source=gmail&amp;ust=1746610312246000&amp;usg=AOvVaw2-4ss1xPwQTdGLeIhY4Kv4">
                                <img alt="instagram" width="31" height="31" src="https://monarchbutterflies.ca/reporting/assets/img/instagram.png" style="margin:0px;width:31px;height:31px" crossorigin="" class="CToWUd" data-bit="iit">
                            </a>
                            </span>
                        </span>
                        <span style="margin:0px;font-size:11pt;color:rgb(34,34,34)!important">&nbsp;</span>
                        <span style="margin:0px;font-size:11pt;color:rgb(34,34,34)!important">
                            <span style="margin:0px;border:none;display:inline-block;overflow:hidden;width:31px;height:31px">
                            <a href="https://www.pinterest.ca/livemonarchs" rel="noopener noreferrer" title="https://www.pinterest.ca/livemonarchs" style="color:rgb(17,85,204);margin:0px" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://www.pinterest.ca/livemonarchs&amp;source=gmail&amp;ust=1746610312246000&amp;usg=AOvVaw0wbYtfwdaC-8iMMI1PHr0Z">
                                <img alt="pinterest" width="31" height="31" src="https://monarchbutterflies.ca/reporting/assets/img/pinterest.png" style="margin:0px;width:31px;height:31px" crossorigin="" class="CToWUd" data-bit="iit">
                            </a>
                            </span>
                        </span>
                        <span style="margin:0px">&nbsp;</span>
                        <span style="margin:0px;font-size:11pt;color:rgb(34,34,34)!important">
                            <span style="margin:0px;border:none;display:inline-block;overflow:hidden;width:31px;height:31px">
                            <a href="https://www.youtube.com/channel/UCEY2PmovSdUk3-4iglkS0dw" rel="noopener noreferrer" title="https://www.youtube.com/channel/UCEY2PmovSdUk3-4iglkS0dw" style="color:rgb(17,85,204);margin:0px" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://www.youtube.com/channel/UCEY2PmovSdUk3-4iglkS0dw&amp;source=gmail&amp;ust=1746610312246000&amp;usg=AOvVaw37TluM-IKvqNPYBEpxyWBN">
                                <img alt="youtube" width="31" height="31" src="https://monarchbutterflies.ca/reporting/assets/img/youtube.png" style="margin:0px;width:31px;height:31px" crossorigin="" class="CToWUd" data-bit="iit">
                            </a>
                            </span>
                        </span>
                        </p>
                        <p dir="ltr" style="color:rgb(0,0,0)!important;margin-top:0pt;margin-bottom:0pt;line-height:1.656">
                        <span style="margin:0px;font-weight:700;font-size:8pt;font-family:&quot;Courier New&quot;;color:rgb(34,34,34)!important">NOTICE OF CONFIDENTIALITY</span>
                        </p>
                        <p dir="ltr" style="color:rgb(0,0,0)!important;margin-top:0pt;margin-bottom:0pt;line-height:1.656">
                        <span style="margin:0px;font-size:8pt;font-family:&quot;Courier New&quot;;color:rgb(34,34,34)!important">This communication including any information transmitted with it is intended only for the use of the addressees and is confidential. If you are not an intended recipient or responsible for delivering the message to an intended recipient, any review, disclosure, conversion to hard copy, dissemination, reproduction or other use of any part of this communication is strictly prohibited, as is the taking or omitting of any action in reliance upon this communication. If you receive this communication in error or without authorization please notify us immediately by return e-mail or otherwise and permanently delete the entire communication from any computer, disk drive, or other storage medium.</span>
                        </p>
                    </div>
                    </div>
                </div>
                </div>
            </div>';

            $headers = ['Content-Type: text/html; charset=UTF-8'];

            if (wp_mail($recipients, $subject, $message, $headers)) {
                echo " Email sent successfully!";
                echo "<br />Email Sent";
                echo "<br />" . $cnt . ") Order ID: " . $orderID;
                echo " | Order Tracking: " . $eventDescription;
                $update_query = "UPDATE `inv_woo_orders` SET `is_email` = '1' WHERE `order_id` = " . $orderID;
                $DB->query($update_query);
                return true;
            } else {
                //echo "Email failed to send.";
            }
        }
    }    
}

function update_order_tracking_status($conn, $order_id, $event_description)
{
    //$conn->query($meta_query);        
    $eventDescription = $conn->real_escape_string($event_description);
    $updateQuery	= "UPDATE `inv_woo_orders` SET `tracking_event_description` = '" . $eventDescription . "' WHERE `order_id` = '" . $order_id . "'";
    $conn->query($updateQuery);

    $order_notes = "Shipping Tracking Status changed to: " . $eventDescription;
    $insert      = "INSERT INTO `inv_woo_order_notes`(`order_id`, `order_notes`, `datetime`) VALUES($order_id, '" . $order_notes . "', '" . date('Y-m-d H:i:s') . "')";
    $conn->query($insert) or die($insert);

    $deliveredEvents = [
        'Delivered',
        'Delivered to your community mailbox, parcel locker or apt./condo mailbox',
        "Delivered to recipient's front door",
        "Delivered to recipient&#039;s front door",
        'Delivered to your concierge or building manager',
        "Notice card left indicating where and when to pick up item",
        "Photo available",
        "Item available for pickup at Post Office",
        "Delivered to recipient's back door",
        "Delivered to recipient&#039;s back door",
        "Delivered to recipient's side door",
        "Delivered to recipient&#039;s side door",
        "Item redirected to post office for pickup",
        "Delivered at or in recipient's garage",
        "Delivered at or in recipient&#039;s garage",
        "Delivered to mailroom",
        "Delivered to recipient&#039;s delivery partner",
        "Delivered to recipient's delivery partner",
        "Delivered to recipient&#039;s safe drop location",
        "Delivered to recipient's safe drop location",	
        "Final Notice&#059; Item will be returned to sender if not collected within 10 days"
    ];  
    if (in_array($eventDescription, $deliveredEvents, true)) {
        echo "<br />Email Sent";
        sendCustomerEmail($conn,$order_id);
    }
}

function sync_order_status($conn, $order_id, $tracking_number) {
    // Canada Post API credentials
    $api_username   = 'b852cbebf491d513';
    $api_password   = 'e10e82b932985b2f87a89d';    
    $summaryXml     = '';
    $detailXml      = '';

    $api_url        = "https://soa-gw.canadapost.ca/vis/track/pin/$tracking_number/summary";
    // Initialize cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($ch, CURLOPT_USERPWD, "$api_username:$api_password");

    // Execute API request
    $response = curl_exec($ch);
    $http_status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($http_status == 200) {
        $xml = simplexml_load_string($response);
        $summaryXml = $xml;
        $summaryData = parseTrackingSummary($summaryXml);
        $summarySize = sizeof($summaryData);
        if(sizeof($summaryData) > 0)
        {
            foreach ($summaryData as $key => $value)
            {
                $eventTitle = htmlspecialchars($key);
                $eventDescription = htmlspecialchars($value);
                if($eventTitle == 'Event Description' && $eventDescription != '')
                {
                    update_order_tracking_status($conn, $order_id, $eventDescription); 
                    echo $eventDescription;
                }
            }
        }
    }
}

$meta_query = "SELECT order_id, status, tracking_id FROM `inv_woo_orders` Where `is_email` = '0' AND `status` = 'shipped-update' AND `tracking_id` != 0 AND `tracking_id` != '' ORDER BY `order_id` ASC";
$meta_conn	= $conn->query($meta_query);
if(mysqli_num_rows($meta_conn) > 0)
{
    $cnt = 1;
    while($meta_fetch = mysqli_fetch_object($meta_conn))
    {
        $orderID = $meta_fetch->order_id;
        echo "<br />" . $cnt . ") Order ID: " . $orderID;
        echo " | Order Status: " . $meta_fetch->status;
        echo " | Order Tracking: " . $meta_fetch->tracking_id;
        $tracking_number = $meta_fetch->tracking_id;
        sync_order_status($conn, $orderID, $tracking_number);

        /*echo "<br />" . $meta_update = "UPDATE `inv_woo_orders` SET `status` = 'completed-pendin2' Where `order_id` = " . $orderID;
        $conn->query($meta_update);* /
        $meta_insert = "INSERT INTO `inv_woo_sync_logs` (`order_id`, `notes`, `datetime`) VALUES('" . $orderID . "', 'CP Status changed ', '" . date('Y-m-d H:i:s') . "')";
        $conn->query($meta_insert);*/
        echo "<hr />";
        $cnt++;
    }      
}

/*$meta_query = "SELECT order_id, status, tracking_id, tracking_event_description  FROM `inv_woo_orders` Where `is_email` = '0' AND `status` = 'shipped-update' AND `tracking_id` != 0 AND `tracking_id` != '' ORDER BY `order_id` ASC";
$meta_conn	= $conn->query($meta_query);
if(mysqli_num_rows($meta_conn) > 0)
{
    $cnt = 1;
    $deliverCnt = 0;
    while($meta_fetch = mysqli_fetch_object($meta_conn))
    {
        $orderID = $meta_fetch->order_id;
        echo "<br />" . $cnt . ") Order ID: " . $orderID;
        echo " | Order Status: " . $meta_fetch->status;
        echo " | Order Tracking: " . $meta_fetch->tracking_id;
        $tracking_number = $meta_fetch->tracking_id;
        echo " | Order Tracking: " . $eventDescription = $conn->real_escape_string($meta_fetch->tracking_event_description);
        $deliveredEvents = [
            'Delivered',
            'Delivered to your community mailbox, parcel locker or apt./condo mailbox',
            "Delivered to recipient's front door",
            "Delivered to recipient&#039;s front door",
            'Delivered to your concierge or building manager',
            "Notice card left indicating where and when to pick up item",
            "Photo available",
            "Item available for pickup at Post Office",
            "Delivered to recipient's back door",
            "Delivered to recipient&#039;s back door",
            "Delivered to recipient's side door",
            "Delivered to recipient&#039;s side door",
            "Item redirected to post office for pickup",
            "Delivered at or in recipient's garage",
            "Delivered at or in recipient&#039;s garage",
            "Delivered to mailroom",
            "Delivered to recipient&#039;s delivery partner",
            "Delivered to recipient's delivery partner",
            "Delivered to recipient&#039;s safe drop location",
            "Delivered to recipient's safe drop location",	
            "Final Notice&#059; Item will be returned to sender if not collected within 10 days"
        ];  
        if (in_array($eventDescription, $deliveredEvents, true)) {
            echo "<br />Email Sent";
            echo "<br />" . $cnt . ") Order ID: " . $orderID;
            echo " | Order Tracking: " . $eventDescription;
            $update_query = "UPDATE `inv_woo_orders` SET `is_email` = '1' WHERE `order_id` = " . $orderID;
            $conn->query($update_query);
            $deliverCnt++;
        }
 
        echo "<hr />";
        $cnt++;
    }      
    echo "<br />Deliver Count : " . $deliverCnt;
}*/
?>