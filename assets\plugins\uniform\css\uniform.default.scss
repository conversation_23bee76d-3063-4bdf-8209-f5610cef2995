/*

Uniform Theme: Uniform Default
Version: 1.8
By: <PERSON>
License: MIT License
---
For use with the Uniform plugin:
http://uniformjs.com/

*/

$button-height: 30px;
$button-margin-left: 13px;
$button-padding: 8px 15px 0 2px;
$button-span-height: 22px;
$checkbox-height: 19px;
$checkbox-width: 19px;
$input-padding: 3px;
$radio-height: 18px;
$radio-width: 18px;
$select-fixed-width: 190px;
$select-height: 26px;
$select-margin-left: 10px;
$select-margin-right: 25px;
$select-select-height: 22px;
$select-select-top: 2px;
$upload-action-width: 82px;
$upload-filename-margin-top: 2px;
$upload-filename-margin-bottom: 2px;
$upload-filename-margin-left: 2px;
$upload-filename-width: 85px;
$upload-filename-padding: 0 10px;
$upload-height: 28px;
$upload-width: 190px;

@import "../../_base/css/uniform._base.scss";

/* INPUT & TEXTAREA */

#{$class-wrapper-element}#{$class-wrapper} input#{$class-input},
#{$class-wrapper-element}#{$class-wrapper} select#{$class-multiselect},
#{$class-wrapper-element}#{$class-wrapper} textarea#{$class-textarea} {
	font-size: 12px;
	font-family: "Helvetica Neue", Arial, Helvetica, sans-serif;
	font-weight: normal;
	color: #777;
	border-top: solid 1px #aaa;
	border-left: solid 1px #aaa;
	border-bottom: solid 1px #ccc;
	border-right: solid 1px #ccc;
	@include border-radius($input-padding);

	@include whenHover {
		@include box-shadow(0px 0px 4px rgba(0,0,0,0.3));
		border-color: #999;
	}
}

/* PRESENTATION */

/* Buttons */

div#{$class-wrapper}#{$class-button} {
	span {
		font-weight: bold;
		font-family: "Helvetica Neue", Arial, Helvetica, sans-serif;
		font-size: 12px;
		letter-spacing: 1px;
		text-transform: uppercase;
	}

	@include whenHover {
		span {
			color: #555;
		}
	}

	@include whenDisabled {
		span {
			color: #bbb;
		}
	}
}


/* Select */

div#{$class-wrapper}#{$class-select} {
	font-size: 12px;

	span {
		color: #666;
		text-shadow: 0 1px 0 #fff;
	}

	select {
		font-family: "Helvetica Neue", Arial, Helvetica, sans-serif;
		font-size: 12px;
	}

	@include whenDisabled {
		span {
			color: #bbb;
		}
	}
}

/* Checker */
div#{$class-wrapper}#{$class-checkbox} {
	margin-right: 5px;
}

/* Radio */
div#{$class-wrapper}#{$class-radio} {
	margin-right: 3px;
}

/* Uploader */
div#{$class-wrapper}#{$class-upload} {
	span#{$class-action} {
		text-shadow: #fff 0px 1px 0px;
		background-color: #fff;
		font-size: 11px;
		font-weight: bold;
	}

	span#{$class-filename} {
		color: #777;
		border-right: solid 1px #bbb;
		font-size: 11px;
	}

	@include whenDisabled {
		span#{$class-action} {
			color: #aaa;
		}

		span#{$class-filename} {
			border-color: #ddd;
			color: #aaa;
		}
	}
}

#{$class-wrapper-element}#{$class-wrapper} input#{$class-input} {
	&, &:focus {
		background-color: #fff;
	}
}
