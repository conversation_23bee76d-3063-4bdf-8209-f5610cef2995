{"packages": [{"name": "setasign/fpdf", "version": "1.8.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Setasign/FPDF.git", "reference": "b0ddd9c5b98ced8230ef38534f6f3c17308a7974"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDF/zipball/b0ddd9c5b98ced8230ef38534f6f3c17308a7974", "reference": "b0ddd9c5b98ced8230ef38534f6f3c17308a7974", "shasum": ""}, "require": {"ext-gd": "*", "ext-zlib": "*"}, "time": "2021-08-30T07:50:06+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["fpdf.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://fpdf.org/"}], "description": "FPDF is a PHP class which allows to generate PDF files with pure PHP. F from FPDF stands for Free: you may use it for any kind of usage and modify it to suit your needs.", "homepage": "http://www.fpdf.org", "keywords": ["fpdf", "pdf"], "support": {"source": "https://github.com/Setasign/FPDF/tree/1.8.4"}, "install-path": "../setasign/fpdf"}, {"name": "setasign/fpdi", "version": "v2.3.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "6231e315f73e4f62d72b73f3d6d78ff0eed93c31"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/6231e315f73e4f62d72b73f3d6d78ff0eed93c31", "reference": "6231e315f73e4f62d72b73f3d6d78ff0eed93c31", "shasum": ""}, "require": {"ext-zlib": "*", "php": "^5.6 || ^7.0 || ^8.0"}, "conflict": {"setasign/tfpdf": "<1.31"}, "require-dev": {"phpunit/phpunit": "~5.7", "setasign/fpdf": "~1.8", "setasign/tfpdf": "1.31", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "~6.2"}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use TCPDF or tFPDF as an alternative. There's no fixed dependency configured."}, "time": "2021-02-11T11:37:01+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"setasign\\Fpdi\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "support": {"issues": "https://github.com/Setasign/FPDI/issues", "source": "https://github.com/Setasign/FPDI/tree/v2.3.6"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/setasign/fpdi", "type": "tidelift"}], "install-path": "../setasign/fpdi"}], "dev": false, "dev-package-names": []}