<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>SetKeywords</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>SetKeywords</h1>
<code>SetKeywords(<b>string</b> keywords [, <b>boolean</b> isUTF8])</code>
<h2>Description</h2>
Associates keywords with the document, generally in the form 'keyword1 keyword2 ...'.
<h2>Parameters</h2>
<dl class="param">
<dt><code>keywords</code></dt>
<dd>
The list of keywords.
</dd>
<dt><code>isUTF8</code></dt>
<dd>
Indicates if the string is encoded in ISO-8859-1 (<code>false</code>) or UTF-8 (<code>true</code>).<br>
Default value: <code>false</code>.
</dd>
</dl>
<h2>See also</h2>
<a href="setauthor.htm">SetAuthor</a>,
<a href="setcreator.htm">SetCreator</a>,
<a href="setsubject.htm">SetSubject</a>,
<a href="settitle.htm">SetTitle</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
