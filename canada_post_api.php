<?php
// Canada Post API credentials
$api_key = 'b852cbebf491d513';
$api_secret = '7516a83437a13f4191cd18';
$customer_number = '8969616';  // CPC customer number
$contract_id = '********';  // Optional, depending on your Canada Post account

// API base URL (use production for live, sandbox for testing)
$api_url = 'https://soa-gw.canadapost.ca';//'https://ct.soa-gw.canadapost.ca';  // Use https://soa-gw.canadapost.ca for production

// Prepare request URL for creating a shipment
$request_url = $api_url . '/rs/ship/ment/';

// Prepare shipping information (replace with actual data)
$shipment_data = <<<XML
<shipment xmlns="http://www.canadapost.ca/ws/shipment-v8">
  <customer-request-id>ABC123</customer-request-id>
  <shipping-point-id>$customer_number</shipping-point-id>
  <parcel-characteristics>
    <weight>1</weight>
  </parcel-characteristics>
  <destination>
    <domestic>
      <address>
        <address-line-1>1234 Main St</address-line-1>
        <city>Ottawa</city>
        <province>ON</province>
        <postal-code>K1A0B1</postal-code>
      </address>
    </domestic>
  </destination>
  <options>
    <option>
      <option-code>DC</option-code>
    </option>
  </options>
  <print-preferences>
    <output-format>8.5x11</output-format>
  </print-preferences>
  <notification>
    <email><EMAIL></email>
  </notification>
</shipment>
XML;

// Prepare the cURL request to create a shipment
$auth = base64_encode($api_key . ':' . $api_secret);
$ch = curl_init($request_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Basic ' . $auth,
    'Content-Type: application/vnd.cpc.shipment-v8+xml',
    'Accept: application/vnd.cpc.shipment-v8+xml',
]);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $shipment_data);

// Execute the request and get the response
$response = curl_exec($ch);
curl_close($ch);

// Parse the response to get the shipment ID and label URL
if ($response) {
    $response_xml = simplexml_load_string($response);
    
    // Extract the shipment ID and label URL from the response
    $shipment_id = (string)$response_xml->{'shipment-id'};
    $label_url = (string)$response_xml->{'links'}->{'label'};
    
    echo "Shipment ID: " . $shipment_id . "\n";
    echo "Label URL: " . $label_url . "\n";
    
    // Download the label (PDF) and save it locally
    $label_pdf = file_get_contents($label_url);
    file_put_contents('shipping_label.pdf', $label_pdf);
    
    echo "Shipping label saved as 'shipping_label.pdf'\n";
} else {
    echo "Failed to create shipment or fetch the label.\n";
}
?>
