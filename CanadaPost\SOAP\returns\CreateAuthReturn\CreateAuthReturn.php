<?php
/**
 * Sample code for the CreateAuthorizedReturn Canada Post service.
 * 
 * The CreateAuthorizedReturn service is used to create a return shipping label 
 * when the sender address and approximate package weight are known. This type of 
 * label is typically emailed through a self-serve or service agent process to the
 * individual returning the item.
 * 
 * This sample is configured to access the Developer Program sandbox environment. 
 * Use your development key username and password for the web service credentials.
 * 
 */

// Your username and password are imported from the following file
// CPCWS_SOAP_Returns_PHP_Samples\SOAP\returns\user.ini
$userProperties = parse_ini_file(realpath(dirname($_SERVER['SCRIPT_FILENAME'])) . '/../user.ini');

$wsdl = realpath(dirname($_SERVER['SCRIPT_FILENAME'])) . '/../../wsdl/authreturn.wsdl';

$hostName = 'ct.soa-gw.canadapost.ca';

// SOAP URI
$location = 'https://' . $hostName . '/rs/soap/authreturn/v2';

// SSL Options
$opts = array('ssl' =>
	array(
		'verify_peer'=> false,
		'cafile' => realpath(dirname($_SERVER['SCRIPT_FILENAME'])) . '/../../../third-party/cert/cacert.pem',
		'CN_match' => $hostName
	)
);

$ctx = stream_context_create($opts);	
$client = new SoapClient($wsdl,array('location' => $location, 'features' => SOAP_SINGLE_ELEMENT_ARRAYS, 'stream_context' => $ctx));

// Set WS Security UsernameToken
$WSSENS = 'http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd';
$usernameToken = new stdClass(); 
$usernameToken->Username = new SoapVar($userProperties['username'], XSD_STRING, null, null, null, $WSSENS);
$usernameToken->Password = new SoapVar($userProperties['password'], XSD_STRING, null, null, null, $WSSENS);
$content = new stdClass(); 
$content->UsernameToken = new SoapVar($usernameToken, SOAP_ENC_OBJECT, null, null, null, $WSSENS);
$header = new SOAPHeader($WSSENS, 'Security', $content);
$client->__setSoapHeaders($header); 

try {
	$mailedBy = $userProperties['customerNumber'];
	$contractId = '0042708517';

	// Execute Request
	$result = $client->__soapCall('CreateAuthorizedReturn', array(
	    'create-authorized-return-request' => array(
			'locale'			=> 'EN',
			'mailed-by'			=> $mailedBy,
			'authorized-return' 		=> array(
				'service-code'		=> 'DOM.EP',
				'returner'			=> array(
					'name'				=> 'Bulma',
					'company'			=> 'company',	
					'domestic-address'	=> array(
						'address-line-1'	=> '502 MAIN ST N',	
						'city'				=> 'MONTREAL',	
						'province'		=> 'QC',	
						'postal-code'	=> 'H2B1A0'		
					)
				),
				'receiver'			=> array(
					'name'				=> 'John Doe',	
					'company'			=> 'ACME Corp',	
					'domestic-address'	=> array(
						'address-line-1'	=> '123 Postal Drive',	
						'city'				=> 'Ottawa',	
						'province'		=> 'ON',	
						'postal-code'	=> 'K1P5Z9'		
					)					
				),
				'parcel-characteristics'	=> array(
					'weight'		=> 15
				),
				'print-preferences' 	=> array(
					'encoding'	=> 'PDF'
				),
				'settlement-info' 	=> array(
					'contract-id'	=> $contractId
				)																		
			)
		)
	), NULL, NULL);
	
	// Parse Response
	if ( isset($result->{'authorized-return-info'}) ) {
	    echo  'Tracking Pin: ' . $result->{'authorized-return-info'}->{'tracking-pin'} . "\n";                 
		foreach ( $result->{'authorized-return-info'}->{'artifacts'}->{'artifact'} as $artifact ) {  
			echo 'Artifact Id: ' . $artifact->{'artifact-id'} . "\n";
		}
	} else {
		foreach ( $result->{'messages'}->{'message'} as $message ) {
			echo 'Error Code: ' . $message->code . "\n";
			echo 'Error Msg: ' . $message->description . "\n\n";
		}
	}
	
} catch (SoapFault $exception) {
	echo 'Fault Code: ' . trim($exception->faultcode) . "\n"; 
	echo 'Fault Reason: ' . trim($exception->getMessage()) . "\n"; 
}

?>

