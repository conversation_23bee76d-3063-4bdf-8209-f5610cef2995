
html, body {
    margin: 0;
    background: #FFFFFF;
    font-family: Lucida Grande, Tahoma, sans-serif;
    font-size: 11px;
    overflow: hidden;
}

a {
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

.toolbar {
    height: 14px;
    border-top: 1px solid ThreeDHighlight;
    border-bottom: 1px solid ThreeDShadow;
    padding: 2px 6px;
    background: ThreeDFace;
}

.toolbarRight {
    position: absolute;
    top: 4px;
    right: 6px;
}

#log {
    overflow: auto;
    position: absolute;
    left: 0;
    width: 100%;
}

#commandLine {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 18px;
    border: none;
    border-top: 1px solid ThreeDShadow;
}

/************************************************************************************************/

.logRow {
    position: relative;
    border-bottom: 1px solid #D7D7D7;
    padding: 2px 4px 1px 6px;
    background-color: #FFFFFF;
}

.logRow-command {
    font-family: Monaco, monospace;
    color: blue;
}

.objectBox-null {
    padding: 0 2px;
    border: 1px solid #666666;
    background-color: #888888;
    color: #FFFFFF;
}

.objectBox-string {
    font-family: Monaco, monospace;
    color: red;
    white-space: pre;
}

.objectBox-number {
    color: #000088;
}

.objectBox-function {
    font-family: Monaco, monospace;
    color: DarkGreen;
}

.objectBox-object {
    color: DarkGreen;
    font-weight: bold;
}

/************************************************************************************************/

.logRow-info,
.logRow-error,
.logRow-warning {
    background: #FFFFFF no-repeat 2px 2px;
    padding-left: 20px;
    padding-bottom: 3px;
}

.logRow-info {
    background-image: url(infoIcon.png);
}

.logRow-warning {
    background-color: cyan;
    background-image: url(warningIcon.png);
}

.logRow-error {
    background-color: LightYellow;
    background-image: url(errorIcon.png);
}

.errorMessage {
    vertical-align: top;
    color: #FF0000;
}

.objectBox-sourceLink {
    position: absolute;
    right: 4px;
    top: 2px;
    padding-left: 8px;
    font-family: Lucida Grande, sans-serif;
    font-weight: bold;
    color: #0000FF;
}

/************************************************************************************************/

.logRow-group {
    background: #EEEEEE;
    border-bottom: none;
}

.logGroup {
    background: #EEEEEE;
}

.logGroupBox {
    margin-left: 24px;
    border-top: 1px solid #D7D7D7;
    border-left: 1px solid #D7D7D7;
}

/************************************************************************************************/

.selectorTag,
.selectorId,
.selectorClass {
    font-family: Monaco, monospace;
    font-weight: normal;
}

.selectorTag {
    color: #0000FF;
}

.selectorId {
    color: DarkBlue;
}

.selectorClass {
    color: red;
}

/************************************************************************************************/

.objectBox-element {
    font-family: Monaco, monospace;
    color: #000088;
}

.nodeChildren {
    margin-left: 16px;
}

.nodeTag {
    color: blue;
}

.nodeValue {
    color: #FF0000;
    font-weight: normal;
}

.nodeText,
.nodeComment {
    margin: 0 2px;
    vertical-align: top;
}

.nodeText {
    color: #333333;
}

.nodeComment {
    color: DarkGreen;
}

/************************************************************************************************/

.propertyNameCell {
    vertical-align: top;
}

.propertyName {
    font-weight: bold;
}
