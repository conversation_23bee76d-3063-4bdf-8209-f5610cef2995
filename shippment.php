<?php
// Step 1: Set Up API Integration
function getCanadaPostCredentials() {
    return [
        'username' => 'b852cbebf491d513',
        'password' => '7516a83437a13f4191cd18',
        'customer_number' => '8969616',//'0008969616',
        'contract_id' => '43194516'
    ];
}



// Replace these with your actual production API credentials
$username = 'b852cbebf491d513';
$password = '7516a83437a13f4191cd18';
$customer_number = '8969616';
$contract_id = '43194516';

// Production base URL
$base_url = 'https://soa-gw.canadapost.ca';

// Create shipment XML data with expected-mailing-date
$shipmentData = <<<XML
<shipment xmlns="http://www.canadapost.ca/ws/shipment-v8">
    <customer-request-id>1</customer-request-id>
    <expected-mailing-date>2024-08-06</expected-mailing-date>
    <delivery-spec>
        <service-code>DOM.XP</service-code>
        <sender>
            <company>Sender Company</company>
            <contact-phone>1234567890</contact-phone>
            <address-details>
                <address-line-1>123 Main Street</address-line-1>
                <city>Montreal</city>
                <prov-state>QC</prov-state>
                <postal-zip-code>H2B2Y5</postal-zip-code>
                <country-code>CA</country-code>
            </address-details>
        </sender>
        <destination>
            <company>Receiver Company</company>
            <address-details>
                <address-line-1>456 Another St</address-line-1>
                <city>Toronto</city>
                <prov-state>ON</prov-state>
                <postal-zip-code>M5H2N2</postal-zip-code>
                <country-code>CA</country-code>
            </address-details>
        </destination>
        <parcel-characteristics>
            <weight>1</weight>
        </parcel-characteristics>
    </delivery-spec>
</shipment>
XML;

function sendRequest($url, $username, $password, $xmlData = null) {
    $ch = curl_init($url);

    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($ch, CURLOPT_USERPWD, "{$username}:{$password}");

    if ($xmlData) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $xmlData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/vnd.cpc.shipment-v8+xml',
            'Accept: application/vnd.cpc.shipment-v8+xml'
        ]);
    }

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    if (curl_errno($ch)) {
        throw new Exception('CURL error: ' . curl_error($ch));
    }

    curl_close($ch);

    return ['response' => $response, 'http_code' => $http_code];
}

try {
    // Step 1: Create Shipment
    $createShipmentUrl = "{$base_url}/rs/{$customer_number}/{$customer_number}/shipment";
    $createShipmentResult = sendRequest($createShipmentUrl, $username, $password, $shipmentData);

    // Log the full response for debugging
    file_put_contents('createShipmentResponse.xml', $createShipmentResult['response']);

    if ($createShipmentResult['http_code'] !== 200) {
        throw new Exception('Error creating shipment: HTTP Code ' . $createShipmentResult['http_code'] . ' Response: ' . $createShipmentResult['response']);
    }

    // Try to parse the response as XML
    libxml_use_internal_errors(true);
    $shipmentXml = simplexml_load_string($createShipmentResult['response']);
    if ($shipmentXml === false) {
        throw new Exception('Failed loading XML: ' . implode(", ", libxml_get_errors()));
    }
    $shipmentId = (string)$shipmentXml->{'shipment-id'};

    // Step 2: Retrieve Label
    $labelUrl = "{$base_url}/rs/{$customer_number}/{$customer_number}/shipment/{$shipmentId}/label";
    $labelResult = sendRequest($labelUrl, $username, $password);

    // Log the full response for debugging
    file_put_contents('labelResponse.pdf', $labelResult['response']);

    if ($labelResult['http_code'] !== 200) {
        throw new Exception('Error retrieving label: HTTP Code ' . $labelResult['http_code'] . ' Response: ' . $labelResult['response']);
    }

    // Check if the response is a valid PDF
    if (stripos($labelResult['response'], '%PDF') === false) {
        throw new Exception('Error retrieving label: Invalid PDF response');
    }

    header('Content-Type: application/pdf');
    echo $labelResult['response'];

} catch (Exception $e) {
    // Output the full error message
    echo 'Error: ' . $e->getMessage();
}




/*
// Step 2: Create API Request Function
function createCanadaPostShipment($shipmentData) {
    $credentials = getCanadaPostCredentials();
    //$endpoint = 'https://ct.soa-gw.canadapost.ca/rs/ship/price'; // Change to the correct endpoint
    $endpoint = 'https://soa-gw.canadapost.ca/rs/ship/price'; //<customer_number>/<customer_number>/shipment'
    $auth = base64_encode($credentials['username'] . ':' . $credentials['password']);
    $headers = [
        'Authorization: Basic ' . $auth,
        'Content-Type: application/vnd.cpc.shipment-v8+xml',
        'Accept: application/vnd.cpc.shipment-v8+xml'
    ];

    $xmlData = createShipmentXML($shipmentData); // Function to convert data to XML
    $ch = curl_init($endpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $xmlData);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    if (curl_errno($ch)) {
        echo 'cURL Error: ' . curl_error($ch);
    }
    curl_close($ch);

    if ($httpCode != 200) {
        echo 'HTTP Error Code: ' . $httpCode . "\n";
        echo 'Response: ' . $response;
        return ['error' => 'Invalid response from Canada Post'];
    }

    return handleCanadaPostResponse($response); // Function to handle the API response
}

// Step 3: Format Shipment Data to XML
function createShipmentXML($data) {
    // Build XML structure based on Canada Post API requirements
    $xml = new SimpleXMLElement('<shipment></shipment>');
    $xml->addChild('customer-number', $data['customer_number']);
    
    $parcel = $xml->addChild('parcel-characteristics');
    $parcel->addChild('weight', $data['weight']);
    
    $destination = $xml->addChild('destination');
    $domestic = $destination->addChild('domestic');
    $domestic->addChild('postal-code', $data['postal_code']);
    
    // Add other necessary fields as needed...
    return $xml->asXML();
}

// Step 4: Handle API Response
function handleCanadaPostResponse($response) {
    $xml = simplexml_load_string($response);
    if ($xml === false) {
        // Handle error
        return ['error' => 'Invalid response from Canada Post'];
    }

    // Extract shipping label URL or PDF from response
    $labelUrl = (string) $xml->{'shipment-id'};
    return ['label_url' => $labelUrl];
}

// Step 5: Generate and Display Label
$shipmentData = [
    'customer_number' => '8368668',
    'weight' => '1.0',
    'postal_code' => 'K1A0B1', // Example postal code
    // Other necessary shipment details...
];

$label = createCanadaPostShipment($shipmentData);
if (isset($label['error'])) {
    echo 'Error: ' . $label['error'];
} else {
    echo 'Shipping Label: <a href="' . $label['label_url'] . '">Download Label</a>';
}*/
?>
