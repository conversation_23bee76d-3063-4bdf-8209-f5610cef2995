<?php return array(
    'root' => array(
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => NULL,
        'name' => '__root__',
        'dev' => false,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => NULL,
            'dev_requirement' => false,
        ),
        'setasign/fpdf' => array(
            'pretty_version' => '1.8.4',
            'version' => '1.8.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../setasign/fpdf',
            'aliases' => array(),
            'reference' => 'b0ddd9c5b98ced8230ef38534f6f3c17308a7974',
            'dev_requirement' => false,
        ),
        'setasign/fpdi' => array(
            'pretty_version' => 'v2.3.6',
            'version' => '2.3.6.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../setasign/fpdi',
            'aliases' => array(),
            'reference' => '6231e315f73e4f62d72b73f3d6d78ff0eed93c31',
            'dev_requirement' => false,
        ),
    ),
);
