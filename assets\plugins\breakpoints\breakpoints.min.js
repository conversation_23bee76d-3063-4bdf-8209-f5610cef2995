(function(c){var b=0;var a=null;c.fn.resetBreakpoints=function(){c(window).unbind("resize");if(a){clearInterval(a)}b=0};c.fn.setBreakpoints=function(e){var d=jQuery.extend({distinct:true,breakpoints:new Array(320,480,768,1024)},e);a=setInterval(function(){var h=c(window).width();var g=false;for(var i in d.breakpoints.sort(function(k,j){return(j-k)})){if(!g&&h>=d.breakpoints[i]&&b<d.breakpoints[i]){if(d.distinct){for(var f in d.breakpoints.sort(function(k,j){return(j-k)})){if(c("body").hasClass("breakpoint-"+d.breakpoints[f])){c("body").removeClass("breakpoint-"+d.breakpoints[f]);c(window).trigger("exitBreakpoint"+d.breakpoints[f])}}g=true}c("body").addClass("breakpoint-"+d.breakpoints[i]);c(window).trigger("enterBreakpoint"+d.breakpoints[i])}if(h<d.breakpoints[i]&&b>=d.breakpoints[i]){c("body").removeClass("breakpoint-"+d.breakpoints[i]);c(window).trigger("exitBreakpoint"+d.breakpoints[i])}if(d.distinct&&h>=d.breakpoints[i]&&h<d.breakpoints[i-1]&&b>h&&b>0&&!c("body").hasClass("breakpoint-"+d.breakpoints[i])){c("body").addClass("breakpoint-"+d.breakpoints[i]);c(window).trigger("enterBreakpoint"+d.breakpoints[i])}}if(b!=h){b=h}},250)}})(jQuery);