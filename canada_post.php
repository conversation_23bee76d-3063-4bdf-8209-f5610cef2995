<?php

// Replace these with your actual API credentials
$username = '6d8c2641df973fcd';
$password = '055a188d78272daa96eade';
$customer_number = '8969616';
$contract_id = '';


// Create shipment XML data
$shipmentData = <<<XML
<shipment xmlns="http://www.canadapost.ca/ws/shipment-v8">
    <customer-number>{$customer_number}</customer-number>
    <parcel-characteristics>
        <weight>1</weight>
    </parcel-characteristics>
    <delivery-spec>
        <service-code>DOM.XP</service-code>
        <sender>
            <company>Sender Company</company>
            <contact-phone>1234567890</contact-phone>
            <address-details>
                <address-line-1>123 Main Street</address-line-1>
                <city>Montreal</city>
                <prov-state>QC</prov-state>
                <postal-zip-code>H2B2Y5</postal-zip-code>
                <country-code>CA</country-code>
            </address-details>
        </sender>
        <destination>
            <company>Receiver Company</company>
            <contact-phone>0987654321</contact-phone>
            <address-details>
                <address-line-1>456 Another St</address-line-1>
                <city>Toronto</city>
                <prov-state>ON</prov-state>
                <postal-zip-code>M5H2N2</postal-zip-code>
                <country-code>CA</country-code>
            </address-details>
        </destination>
    </delivery-spec>
</shipment>
XML;

function sendRequest($url, $username, $password, $xmlData = null) {
    $ch = curl_init($url);

    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($ch, CURLOPT_USERPWD, "{$username}:{$password}");

    if ($xmlData) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $xmlData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/vnd.cpc.shipment-v8+xml',
            'Accept: application/vnd.cpc.shipment-v8+xml'
        ]);
    }

    $response = curl_exec($ch);

    if (curl_errno($ch)) {
        throw new Exception(curl_error($ch));
    }

    curl_close($ch);

    return $response;
}

try {
    // Step 1: Create Shipment
    $createShipmentUrl = "https://ct.soa-gw.canadapost.ca/rs/{$customer_number}/{$customer_number}/shipment";

    $createShipmentResponse = sendRequest($createShipmentUrl, $username, $password, $shipmentData);

    if (stripos($createShipmentResponse, '<html') !== false) {
        throw new Exception('Error creating shipment: ' . strip_tags($createShipmentResponse));
    }

    $shipmentXml = new SimpleXMLElement($createShipmentResponse);
    $shipmentId = (string)$shipmentXml->{'shipment-id'};

    // Step 2: Retrieve Label
    $labelUrl = "https://ct.soa-gw.canadapost.ca/rs/{$customer_number}/{$customer_number}/shipment/{$shipmentId}/label";

    $labelResponse = sendRequest($labelUrl, $username, $password);

    // Debugging: Output the label response content
    if (stripos($labelResponse, '%PDF') === false) {
        throw new Exception('Error retrieving label: ' . strip_tags($labelResponse));
    }

    header('Content-Type: application/pdf');
    echo $labelResponse;

} catch (Exception $e) {
    // Output the full error message
    echo 'Error: ' . $e->getMessage();
}

?>