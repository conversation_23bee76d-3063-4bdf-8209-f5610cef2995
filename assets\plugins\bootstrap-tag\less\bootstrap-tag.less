.tags {
  display: inline-block;
  padding: 4px 6px;
  margin-bottom: @baseLineHeight / 2;
  color: @gray;
  vertical-align: middle;
  .border-radius(@inputBorderRadius);
  background-color: @inputBackground;
  border: 1px solid @inputBorder;
  .box-shadow(inset 0 1px 1px rgba(0,0,0,.075));
  .transition(~"border linear .2s, box-shadow linear .2s");
  width: 206px;
  
  &-hover {
    border-color: rgba(82,168,236,.8);
    outline: 0;
    outline: thin dotted \9; /* IE6-9 */
    .box-shadow(~"inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(82,168,236,.6)");
  }
  
  &[class*="span"] {
    float: none;
    margin-left: 0;
  }
  
  input[type="text"],
  input[type="text"]:focus {
    border: none;
    display: inline;
    outline: 0;
    margin: 0;
    padding: 0;
    font-size: @baseFontSize * .846;
    line-height: 14px;
    .box-shadow(none);
    width: 100%;
  }
}

.tag {
  display: inline-block;
  padding: 2px 4px;
  font-size: @baseFontSize * .846;
  font-weight: bold;
  line-height: 14px; // ensure proper line-height if floated
  color: @white;
  vertical-align: baseline;
  white-space: nowrap;
  text-shadow: 0 -1px 0 rgba(0,0,0,.25);
  background-color: @grayLight;
  padding-left: 9px;
  padding-right: 9px;
  .border-radius(9px);
  margin-bottom: 5px;
  margin-right: 5px;
  .transition(~"all 0.2s");
  
  &:empty {
    display: none;
  }
  
  // Important (red)
  &-important   { background-color: darken(@errorText, 10%); }
  // Warnings (orange)
  &-warning     { background-color: darken(@orange, 10%); }
  // Success (green)
  &-success     { background-color: darken(@successText, 10%); }
  // Info (turquoise)
  &-info        { background-color: darken(@infoText, 10%); }
  // Inverse (black)
  &-inverse     { background-color: darken(@grayDark, 10%); }
  
  .close {
    font-size: @baseFontSize;
    line-height: 14px;
    margin-left: 7px;
  }
}
