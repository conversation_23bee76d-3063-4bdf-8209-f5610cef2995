<?php
 /**
 * Sample code for the GetMerchantRegistrationInfo Canada Post service.
 * 
 * The GetMerchantRegistrationInfo service is called by the ecommerce platform after 
 * the merchant has completed the Canada Post sign-up process. This call returns
 * merchant data such as customer number and merchant username and password. This
 * information is necessary for the platform to perform web service shipping
 * transactions for the merchant.
 *
 * This sample is configured to access the Developer Program sandbox environment. 
 * Use your development key username and password for the web service credentials.
 * 
 **/

// Your username and password are imported from the following file
// CPCWS_SOAP_Platforms_PHP_Samples\SOAP\platforms\user.ini
$userProperties = parse_ini_file(realpath(dirname($_SERVER['SCRIPT_FILENAME'])) . '/../user.ini');

$wsdl = realpath(dirname($_SERVER['SCRIPT_FILENAME'])) . '/../../wsdl/merchantregistration.wsdl';

$hostName = 'ct.soa-gw.canadapost.ca';

// SOAP URI
$location = 'https://' . $hostName . '/ot/soap/merchant/registration';

// SSL Options
$opts = array('ssl' =>
	array(
		'verify_peer'=> false,
		'cafile' => realpath(dirname($_SERVER['SCRIPT_FILENAME'])) . '/../../../third-party/cert/cacert.pem',
		'CN_match' => $hostName
	)
);

$ctx = stream_context_create($opts);	
$client = new SoapClient($wsdl,array('location' => $location, 'features' => SOAP_SINGLE_ELEMENT_ARRAYS, 'stream_context' => $ctx));

// Set WS Security UsernameToken
$WSSENS = 'http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd';
$usernameToken = new stdClass(); 
$usernameToken->Username = new SoapVar($userProperties['username'], XSD_STRING, null, null, null, $WSSENS);
$usernameToken->Password = new SoapVar($userProperties['password'], XSD_STRING, null, null, null, $WSSENS);
$content = new stdClass(); 
$content->UsernameToken = new SoapVar($usernameToken, SOAP_ENC_OBJECT, null, null, null, $WSSENS);
$header = new SOAPHeader($WSSENS, 'Security', $content);
$client->__setSoapHeaders($header); 

try {
	// Execute Request
	$result = $client->__soapCall('GetMerchantRegistrationInfo', array(
	    'get-merchant-registration-info-request' => array(
			'locale'	=> 'EN',
			'token-id'	=> '1111111111111111111111'
		)
	), NULL, NULL);
	
	// Parse Response
	if ( isset($result->{'merchant-info'}) ) {
		if ( $result->{'merchant-info'}->{'customer-number'} ) {
			echo 'Customer Number: ' . $result->{'merchant-info'}->{'customer-number'} . "\n";			
		}
		if ( $result->{'merchant-info'}->{'contract-number'} ) {
			echo 'Contract Number: ' . $result->{'merchant-info'}->{'contract-number'} . "\n";			
		}
		if ( $result->{'merchant-info'}->{'merchant-username'} ) {
			echo 'Merchant Username: ' . $result->{'merchant-info'}->{'merchant-username'} . "\n";			
		}
		if ( $result->{'merchant-info'}->{'merchant-password'} ) {
			echo 'Merchant Password: ' . $result->{'merchant-info'}->{'merchant-password'} . "\n";			
		}
		echo 'Has Default CC:  ' . $result->{'merchant-info'}->{'has-default-credit-card'} . "\n";
	} else {
		foreach ( $result->{'messages'}->{'message'} as $message ) {
			echo 'Error Code: ' . $message->code . "\n";
			echo 'Error Msg: ' . $message->description . "\n\n";
		}
	}
} catch (SoapFault $exception) {
	echo 'Fault Code: ' . trim($exception->faultcode) . "\n"; 
	echo 'Fault Reason: ' . trim($exception->getMessage()) . "\n"; 
}

?>