Bootstrap Tree v0.2
==============

A lightweight Tree component, built for use with the Twitter Bootstrap framework.

A work in progress. All feedback is welcome.

[10.06.2012] - v0.2 - Added ability to populate branches with Ajax. "Branch" configuration
  specifies where to get the data, and the parameters to "POST" in the request.

Bootstrap Tree &copy; 2012 Cutters Crossing

'Bootstrap' is &copy; 2012 Twitter, Inc.

http://twitter.github.com/bootstrap

Both are licensed under the Apache License, Version 2.0 (the "License")

http://www.apache.org/licenses/LICENSE-2.0