<?php
/**
 * Sample code for the GetOpenReturnTemplates Canada Post service.
 * 
 * The GetOpenReturnTemplates service is used to retrieve the list of active open
 * return templates (based on the mailed-on-behalf-of customer number). Active
 * open return templates are those that still have open return artifacts available
 * for retrieval. Once all of the artifacts from a template have been retrieved,
 * the open return template is consider depleted, and is deleted. 
 * 
 * This sample is configured to access the Developer Program sandbox environment. 
 * Use your development key username and password for the web service credentials.
 * 
 */

// Your username and password are imported from the following file
// CPCWS_SOAP_Returns_PHP_Samples\SOAP\returns\user.ini
$userProperties = parse_ini_file(realpath(dirname($_SERVER['SCRIPT_FILENAME'])) . '/../user.ini');

$wsdl = realpath(dirname($_SERVER['SCRIPT_FILENAME'])) . '/../../wsdl/openreturn.wsdl';

$hostName = 'ct.soa-gw.canadapost.ca';

// SOAP URI
$location = 'https://' . $hostName . '/rs/soap/openreturn/v2';

// SSL Options
$opts = array('ssl' =>
	array(
		'verify_peer'=> false,
		'cafile' => realpath(dirname($_SERVER['SCRIPT_FILENAME'])) . '/../../../third-party/cert/cacert.pem',
		'CN_match' => $hostName
	)
);

$ctx = stream_context_create($opts);	
$client = new SoapClient($wsdl,array('location' => $location, 'features' => SOAP_SINGLE_ELEMENT_ARRAYS, 'stream_context' => $ctx));

// Set WS Security UsernameToken
$WSSENS = 'http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd';
$usernameToken = new stdClass(); 
$usernameToken->Username = new SoapVar($userProperties['username'], XSD_STRING, null, null, null, $WSSENS);
$usernameToken->Password = new SoapVar($userProperties['password'], XSD_STRING, null, null, null, $WSSENS);
$content = new stdClass(); 
$content->UsernameToken = new SoapVar($usernameToken, SOAP_ENC_OBJECT, null, null, null, $WSSENS);
$header = new SOAPHeader($WSSENS, 'Security', $content);
$client->__setSoapHeaders($header); 

try {
	$mailedBy = $userProperties['customerNumber'];

	// Execute Request
	$result = $client->__soapCall('GetOpenReturnTemplates', array(
	    'get-open-return-templates-request' => array(
			'locale'		=> 'EN',
			'mailed-by'		=> $mailedBy,
			'from'			=> '2011-01-01',
			'to'			=> '2012-02-01',
		)
	), NULL, NULL);
	
	// Parse Response
	if ( isset($result->{'templates'}) ) {
		if ( isset($result->{'templates'}->{'template-id'}) ) {
			foreach ( $result->{'templates'}->{'template-id'} as $templateId ) {  
				echo 'Template Id: ' . $templateId . "\n";
			}	
		} else {
			echo 'No templates returned.' . "\n";
		}
	} else {
		foreach ( $result->{'messages'}->{'message'} as $message ) {
			echo 'Error Code: ' . $message->code . "\n";
			echo 'Error Msg: ' . $message->description . "\n\n";
		}
	}
	
} catch (SoapFault $exception) {
	echo 'Fault Code: ' . trim($exception->faultcode) . "\n"; 
	echo 'Fault Reason: ' . trim($exception->getMessage()) . "\n"; 
}

?>

