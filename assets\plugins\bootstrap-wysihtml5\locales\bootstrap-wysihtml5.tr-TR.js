/**
 * Turkish translation for bootstrap-wysihtml5
 */
(function($){
    $.fn.wysihtml5.locale["tr-TR"] = {
        font_styles: {
            normal: "Normal",
            h1: "Başlık 1",
            h2: "Başlık 2",
            h3: "Başlık 3"
        },
        emphasis: {
            bold: "Kalın",
            italic: "İtalik",
            underline: "Altı Çizili"
        },
        lists: {
            unordered: "Sırasız Liste",
            ordered: "Sıralı Liste",
            outdent: "Girintiyi Azalt",
            indent: "Girintiyi Arttır"
        },
        link: {
            insert: "Ekle",
            cancel: "Vazgeç"
        },
        image: {
            insert: "Ekle",
            cancel: "Vazgeç"
        },
        html: {
            edit: "HTML Göster"
        },
        colours: {
            black: "Siyah",
            silver: "<PERSON>ü<PERSON><PERSON><PERSON>",
            gray: "Gri",
            maroon: "<PERSON><PERSON><PERSON><PERSON> Çürüğü",
            red: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
            purple: "<PERSON><PERSON><PERSON>",
            green: "<PERSON><PERSON><PERSON>",
            olive: "<PERSON><PERSON><PERSON>",
            navy: "<PERSON><PERSON><PERSON>",
            blue: "<PERSON><PERSON>",
            orange: "<PERSON><PERSON><PERSON>"
        }
    };
}(jQuery));
