<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1" />
<title>Test for jQuery validate() plugin</title>

<link rel="stylesheet" type="text/css" media="screen" href="../demo/css/screen.css" />
<link rel="stylesheet" href="../../../themes/flora/flora.all.css" type="text/css" media="screen" title="Flora (Default)">

<script src="../lib/jquery.js" type="text/javascript"></script>
<script src="../../../ui/current/ui.tabs.js" type="text/javascript"></script>
<script type="text/javascript" src="../lib/jquery.metadata.js"></script>
<script type="text/javascript" src="../jquery.validate.js"></script>
<script src="firebug/firebug.js" type="text/javascript"></script>

<script type="text/javascript">

$().ready(function() {
	$("#commentForm").validate({debug:true});
	$("#example > ul").tabs();
});
</script>

<style type="text/css">
form.cmxform { width: 470px; }
</style>

</head>
<body>

	<form class="cmxform" id="commentForm" method="get" action="">

		<div id="example" class="flora">
	        <ul>

	            <li><a href="#fragment-1"><span>One</span></a></li>
	            <li><a href="#fragment-2"><span>Two</span></a></li>
	            <li><a href="#fragment-3"><span>Three</span></a></li>
	        </ul>
	        <div id="fragment-1">
					<fieldset>
						<legend>A simple comment form with submit validation and default messages</legend>
						<p>
							<label for="cname">Name (required, at least 2 characters)</label>
							<input id="cname" name="name" class="some other styles {required:true,minLength:2}" />
						<p>
							<label for="cemail">E-Mail (required)</label>
							<input id="cemail" name="email" class="{required:true,email:true}" />
						</p>
						<p>
							<label for="curl">URL (optional)</label>
							<input id="curl" name="url" class="{url:true}" value="" />
						</p>
						<p>
							<label for="ccomment">Your comment (required)</label>
							<textarea id="ccomment" name="comment" class="{required:true}"></textarea>
						</p>
					</fieldset>

	        </div>
	        <div id="fragment-2">
	            Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat.
	            Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat.
	        </div>
	        <div id="fragment-3">
	            Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat.
	            Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat.
	            Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat.
	        </div>
	    </div>
		<p>
			<input class="submit" type="submit" value="Submit"/>
		</p>

	</form>

</body>
</html>
