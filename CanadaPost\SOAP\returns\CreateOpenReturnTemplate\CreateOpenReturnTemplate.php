<?php
/**
 * Sample code for the CreateOpenReturnTemplate Canada Post service.
 * 
 * The CreateOpenReturnTemplate service is used to request creation of generic
 * labels for retrieval and printing. The sender address and parcel weight are
 * unknown. Each label has a unique barcode, but the rest of the data is the same.
 * The labels can be distributed as part of the original shipment, or sent to a
 * specific individual. 
 * 
 * This sample is configured to access the Developer Program sandbox environment. 
 * Use your development key username and password for the web service credentials.
 * 
 */

// Your username and password are imported from the following file
// CPCWS_SOAP_Returns_PHP_Samples\SOAP\returns\user.ini
$userProperties = parse_ini_file(realpath(dirname($_SERVER['SCRIPT_FILENAME'])) . '/../user.ini');

$wsdl = realpath(dirname($_SERVER['SCRIPT_FILENAME'])) . '/../../wsdl/openreturn.wsdl';

$hostName = 'ct.soa-gw.canadapost.ca';

// SOAP URI
$location = 'https://' . $hostName . '/rs/soap/openreturn/v2';

// SSL Options
$opts = array('ssl' =>
	array(
		'verify_peer'=> false,
		'cafile' => realpath(dirname($_SERVER['SCRIPT_FILENAME'])) . '/../../../third-party/cert/cacert.pem',
		'CN_match' => $hostName
	)
);

$ctx = stream_context_create($opts);	
$client = new SoapClient($wsdl,array('location' => $location, 'features' => SOAP_SINGLE_ELEMENT_ARRAYS, 'stream_context' => $ctx));

// Set WS Security UsernameToken
$WSSENS = 'http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd';
$usernameToken = new stdClass(); 
$usernameToken->Username = new SoapVar($userProperties['username'], XSD_STRING, null, null, null, $WSSENS);
$usernameToken->Password = new SoapVar($userProperties['password'], XSD_STRING, null, null, null, $WSSENS);
$content = new stdClass(); 
$content->UsernameToken = new SoapVar($usernameToken, SOAP_ENC_OBJECT, null, null, null, $WSSENS);
$header = new SOAPHeader($WSSENS, 'Security', $content);
$client->__setSoapHeaders($header); 

try {
	$mailedBy = $userProperties['customerNumber'];
	$contractId = '0042708517';

	// Execute Request
	$result = $client->__soapCall('CreateOpenReturnTemplate', array(
	    'create-open-return-template-request' => array(
			'locale'			=> 'EN',
			'mailed-by'			=> $mailedBy,
			'open-return' 		=> array(
				'max-number-of-artifacts' 	=> '10',
				'service-code'				=> 'DOM.EP',
				'receiver'			=> array(
					'domestic-address'	=> array(
						'address-line-1'	=> '123 Postal Drive',	
						'city'				=> 'Ottawa',	
						'province'			=> 'ON',	
						'postal-code'		=> 'K1P5Z9'		
					)					
				),

				'print-preferences' 	=> array(
					'output-format'	=> '8.5x11',
					'encoding'		=> 'PDF'
				),
				'settlement-info' 	=> array(
					'contract-id'	=> $contractId
				)																		
			)
		)
	), NULL, NULL);
	
	// Parse Response
	if ( isset($result->{'open-return-info'}) ) {
	    echo  'Artifacts Remaining: ' . $result->{'open-return-info'}->{'artifacts-remaining'} . "\n";                 
		echo  'Template Id: ' . $result->{'open-return-info'}->{'template-id'} . "\n";                 
	} else {
		foreach ( $result->{'messages'}->{'message'} as $message ) {
			echo 'Error Code: ' . $message->code . "\n";
			echo 'Error Msg: ' . $message->description . "\n\n";
		}
	}
	
} catch (SoapFault $exception) {
	echo 'Fault Code: ' . trim($exception->faultcode) . "\n"; 
	echo 'Fault Reason: ' . trim($exception->getMessage()) . "\n"; 
}

?>

