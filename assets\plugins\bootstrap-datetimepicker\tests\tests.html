<!DOCTYPE html>
<html>
    <head>
        <link rel="stylesheet" href="assets/qunit.css" />
        <script src="assets/qunit.js"></script>
        <script src="assets/qunit-logging.js"></script> <!-- console.log for test failures -->
        <script src="assets/coverage.js"></script>
        <script src="assets/jquery-1.7.1.min.js"></script>
        <script src="../js/bootstrap-datetimepicker.js"></script>

        <style>
            .datetimepicker {
                /* Appended to body, abs-pos off the page */
                position: absolute;
                display: none;
                top: -9999em;
                left: -9999em;
            }
        </style>

        <!-- Utilities -->
        <script src="assets/utils.js"></script>
        <script src="assets/mock.js"></script>

        <!-- Test suites -->
        <script src="suites/formats.js"></script>
        <script src="suites/mouse_navigation/all.js"></script>
        <script src="suites/mouse_navigation/2012.js"></script>
        <script src="suites/mouse_navigation/2011.js"></script>
        <script src="suites/keyboard_navigation/all.js"></script>
        <script src="suites/keyboard_navigation/2012.js"></script>
        <script src="suites/keyboard_navigation/2011.js"></script>
        <script src="suites/component.js"></script>
        <script src="suites/events.js"></script>
        <script src="suites/options.js"></script>
        <script src="suites/inline.js"></script>
    </head>
    <body>
        <h1 id="qunit-header">bootstrap-datetimepicker</h1>
        <h2 id="qunit-banner"></h2>
        <div id="qunit-testrunner-toolbar"></div>
        <h2 id="qunit-userAgent"></h2>
        <ol id="qunit-tests"></ol>
        <div id="qunit-fixture"></div>
    </body>
</html>