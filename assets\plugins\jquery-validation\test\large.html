<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1" />
<title>Test for jQuery validate() plugin</title>

<link rel="stylesheet" type="text/css" media="screen" href="css/screen.css" />
<script src="../lib/jquery.js" type="text/javascript"></script>
<script src="../lib/jquery.metadata.js" type="text/javascript"></script>
<script src="../lib/jquery.ajaxQueue.js" type="text/javascript"></script>
<script src="../jquery.validate.js" type="text/javascript"></script>

<script type="text/javascript">
$().ready(function() {
	$("#commentForm").validate();
});
</script>

<style type="text/css">
#commentForm { width: 500px; }
#commentForm label { width: 250px; display: block; float: left; }
#commentForm label.error, #commentForm input.submit { margin-left: 253px; }
.focus { background-color: red; }
</style>

</head>
<body>
<form class="cmxform" id="commentForm" method="get" action="">
	<fieldset>
		<legend>A simple comment form with submit validation and default messages</legend>
		<p>
			<label for="cname-x0">Name (required, at least 2 characters)</label>
			<input id="cname-x0" name="name-x0" class="some other styles {required:true,minLength:2}" />
		<p>
			<label for="cemail-x0">E-Mail (required)</label>
			<input id="cemail-x0" name="email-x0" class="{required:true,email:true}" />
		</p>
		<p>
			<label for="curl-x0">URL (optional)</label>
			<input id="curl-x0" name="url-x0" class="{url:true}" value="" />
		</p>
		<p>
			<label for="ccomment-x0">Your comment (required)</label>
			<textarea id="ccomment-x0" name="comment-x0" class="{required:true}"></textarea>
		</p>
		<p>
			<label for="cname-x1">Name (required, at least 2 characters)</label>
			<input class="some other styles {required:true,minLength:2}" name="name-x1" id="cname-x1"/>
		</p><p>
			<label for="cemail-x1">E-Mail (required)</label>
			<input class="{required:true,email:true}" name="email-x1" id="cemail-x1"/>
		</p>
		<p>
			<label for="curl-x1">URL (optional)</label>
			<input value="" class="{url:true}" name="url-x1" id="curl-x1"/>
		</p>
		<p>
			<label for="ccomment-x1">Your comment (required)</label>
			<textarea class="{required:true}" name="comment-x1" id="ccomment-x1"></textarea>
		</p>
		<p>
			<label for="cname-x2">Name (required, at least 2 characters)</label>
			<input class="some other styles {required:true,minLength:2}" name="name-x2" id="cname-x2"/>
		</p><p>
			<label for="cemail-x2">E-Mail (required)</label>
			<input class="{required:true,email:true}" name="email-x2" id="cemail-x2"/>
		</p>
		<p>
			<label for="curl-x2">URL (optional)</label>
			<input value="" class="{url:true}" name="url-x2" id="curl-x2"/>
		</p>
		<p>
			<label for="ccomment-x2">Your comment (required)</label>
			<textarea class="{required:true}" name="comment-x2" id="ccomment-x2"></textarea>
		</p>
		<p>
			<label for="cname-x3">Name (required, at least 2 characters)</label>
			<input class="some other styles {required:true,minLength:2}" name="name-x3" id="cname-x3"/>
		</p><p>
			<label for="cemail-x3">E-Mail (required)</label>
			<input class="{required:true,email:true}" name="email-x3" id="cemail-x3"/>
		</p>
		<p>
			<label for="curl-x3">URL (optional)</label>
			<input value="" class="{url:true}" name="url-x3" id="curl-x3"/>
		</p>
		<p>
			<label for="ccomment-x3">Your comment (required)</label>
			<textarea class="{required:true}" name="comment-x3" id="ccomment-x3"></textarea>
		</p>
		<p>
			<label for="cname-x4">Name (required, at least 2 characters)</label>
			<input class="some other styles {required:true,minLength:2}" name="name-x4" id="cname-x4"/>
		</p><p>
			<label for="cemail-x4">E-Mail (required)</label>
			<input class="{required:true,email:true}" name="email-x4" id="cemail-x4"/>
		</p>
		<p>
			<label for="curl-x4">URL (optional)</label>
			<input value="" class="{url:true}" name="url-x4" id="curl-x4"/>
		</p>
		<p>
			<label for="ccomment-x4">Your comment (required)</label>
			<textarea class="{required:true}" name="comment-x4" id="ccomment-x4"></textarea>
		</p>
		<p>
			<label for="cname-x5">Name (required, at least 2 characters)</label>
			<input class="some other styles {required:true,minLength:2}" name="name-x5" id="cname-x5"/>
		</p><p>
			<label for="cemail-x5">E-Mail (required)</label>
			<input class="{required:true,email:true}" name="email-x5" id="cemail-x5"/>
		</p>
		<p>
			<label for="curl-x5">URL (optional)</label>
			<input value="" class="{url:true}" name="url-x5" id="curl-x5"/>
		</p>
		<p>
			<label for="ccomment-x5">Your comment (required)</label>
			<textarea class="{required:true}" name="comment-x5" id="ccomment-x5"></textarea>
		</p>
		<p>
			<label for="cname-x6">Name (required, at least 2 characters)</label>
			<input class="some other styles {required:true,minLength:2}" name="name-x6" id="cname-x6"/>
		</p><p>
			<label for="cemail-x6">E-Mail (required)</label>
			<input class="{required:true,email:true}" name="email-x6" id="cemail-x6"/>
		</p>
		<p>
			<label for="curl-x6">URL (optional)</label>
			<input value="" class="{url:true}" name="url-x6" id="curl-x6"/>
		</p>
		<p>
			<label for="ccomment-x6">Your comment (required)</label>
			<textarea class="{required:true}" name="comment-x6" id="ccomment-x6"></textarea>
		</p>
		<p>
			<label for="cname-x7">Name (required, at least 2 characters)</label>
			<input class="some other styles {required:true,minLength:2}" name="name-x7" id="cname-x7"/>
		</p><p>
			<label for="cemail-x7">E-Mail (required)</label>
			<input class="{required:true,email:true}" name="email-x7" id="cemail-x7"/>
		</p>
		<p>
			<label for="curl-x7">URL (optional)</label>
			<input value="" class="{url:true}" name="url-x7" id="curl-x7"/>
		</p>
		<p>
			<label for="ccomment-x7">Your comment (required)</label>
			<textarea class="{required:true}" name="comment-x7" id="ccomment-x7"></textarea>
		</p>
		<p>
			<label for="cname-x8">Name (required, at least 2 characters)</label>
			<input class="some other styles {required:true,minLength:2}" name="name-x8" id="cname-x8"/>
		</p><p>
			<label for="cemail-x8">E-Mail (required)</label>
			<input class="{required:true,email:true}" name="email-x8" id="cemail-x8"/>
		</p>
		<p>
			<label for="curl-x8">URL (optional)</label>
			<input value="" class="{url:true}" name="url-x8" id="curl-x8"/>
		</p>
		<p>
			<label for="ccomment-x8">Your comment (required)</label>
			<textarea class="{required:true}" name="comment-x8" id="ccomment-x8"></textarea>
		</p>
		<p>
			<label for="cname-x9">Name (required, at least 2 characters)</label>
			<input class="some other styles {required:true,minLength:2}" name="name-x9" id="cname-x9"/>
		</p><p>
			<label for="cemail-x9">E-Mail (required)</label>
			<input class="{required:true,email:true}" name="email-x9" id="cemail-x9"/>
		</p>
		<p>
			<label for="curl-x9">URL (optional)</label>
			<input value="" class="{url:true}" name="url-x9" id="curl-x9"/>
		</p>
		<p>
			<label for="ccomment-x9">Your comment (required)</label>
			<textarea class="{required:true}" name="comment-x9" id="ccomment-x9"></textarea>
		</p>
		<p>
			<input class="submit" type="submit" value="Submit"/>
		</p>
	</fieldset>
</form>

</body>
</html>
