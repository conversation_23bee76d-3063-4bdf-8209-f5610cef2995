/*!
 * Datetimepicker for Bootstrap
 *
 * Copyright 2012 <PERSON>
 * Improvements by <PERSON>
 * Licensed under the Apache License v2.0
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 */

.datetimepicker {
	padding: 4px;
	margin-top: 1px;
	.border-radius(4px);
	&-inline {
		width: 220px;
	}
	direction: ltr;
	&&-rtl {
		direction: rtl;
		table tr td span {
			float: right;
		}
	}
	&-dropdown {
		top: 0;
		left: 0;
		&:before {
			content: '';
			display: inline-block;
			border-left:   7px solid transparent;
			border-right:  7px solid transparent;
			border-bottom: 7px solid #ccc;
			border-bottom-color: rgba(0,0,0,.2);
			position: absolute;
			top: -7px;
			left: 6px;
		}
		&:after {
			content: '';
			display: inline-block;
			border-left:   6px solid transparent;
			border-right:  6px solid transparent;
			border-bottom: 6px solid @white;
			position: absolute;
			top: -6px;
			left: 7px;
		}
	}
	&-dropdown-left {
		top: 0;
		left: 0;
		&:before {
			content: '';
			display: inline-block;
			border-left:   7px solid transparent;
			border-right:  7px solid transparent;
			border-bottom: 7px solid #ccc;
			border-bottom-color: rgba(0,0,0,.2);
			position: absolute;
			top: -7px;
			right: 6px;
		}
		&:after {
			content: '';
			display: inline-block;
			border-left:   6px solid transparent;
			border-right:  6px solid transparent;
			border-bottom: 6px solid @white;
			position: absolute;
			top: -6px;
			right: 7px;
		}
	}
	>div {
		display: none;
	}
	&.days div.datetimepicker-days {
		display: block;
	}
	&.months div.datetimepicker-months {
		display: block;
	}
	&.years div.datetimepicker-years {
		display: block;
	}
	table{
		margin: 0;
	}
	td,
	th{
		text-align: center;
		width: 20px;
		height: 20px;
		.border-radius(4px);

		border: none;
	}
	// Inline display inside a table presents some problems with
	// border and background colors.
	.table-striped & table tr {
		td, th {
			background-color:transparent;
		}
	}
	table tr td {
		&.day:hover {
			background: @grayLighter;
			cursor: pointer;
		}
		&.old,
		&.new {
			color: @grayLight;
		}
		&.disabled,
		&.disabled:hover {
			background: none;
			color: @grayLight;
			cursor: default;
		}
		&.today,
		&.today:hover,
		&.today.disabled,
		&.today.disabled:hover {
			@todayBackground: lighten(@orange, 30%);
			.buttonBackground(@todayBackground, spin(@todayBackground, 20));
		}
		&.active,
		&.active:hover,
		&.active.disabled,
		&.active.disabled:hover {
			.buttonBackground(@btnPrimaryBackground, spin(@btnPrimaryBackground, 20));
			color: #fff;
			text-shadow: 0 -1px 0 rgba(0,0,0,.25);
		}
		span {
			display: block;
			width: 23%;
			height: 54px;
			line-height: 54px;
			float: left;
			margin: 1%;
			cursor: pointer;
			.border-radius(4px);
			&:hover {
				background: @grayLighter;
			}
			&.disabled,
			&.disabled:hover {
				background:none;
				color: @grayLight;
				cursor: default;
			}
			&.active,
			&.active:hover,
			&.active.disabled,
			&.active.disabled:hover {
				.buttonBackground(@btnPrimaryBackground, spin(@btnPrimaryBackground, 20));
				color: #fff;
				text-shadow: 0 -1px 0 rgba(0,0,0,.25);
			}
			&.old {
				color: @grayLight;
			}
		}
	}

	th.switch {
		width: 145px;
	}

	thead tr:first-child th,
	tfoot tr:first-child th {
		cursor: pointer;
		&:hover{
			background: @grayLighter;
		}
	}
	/*.dow {
		border-top: 1px solid #ddd !important;
	}*/
}
.input-append,
.input-prepend {
	&.date {
		.add-on i {
			display: block;
			cursor: pointer;
			width: 16px;
			height: 16px;
		}
	}
}
