<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>Write</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>Write</h1>
<code>Write(<b>float</b> h, <b>string</b> txt [, <b>mixed</b> link])</code>
<h2>Description</h2>
This method prints text from the current position. When the right margin is reached (or the \n
character is met) a line break occurs and text continues from the left margin. Upon method exit,
the current position is left just at the end of the text.
<br>
It is possible to put a link on the text.
<h2>Parameters</h2>
<dl class="param">
<dt><code>h</code></dt>
<dd>
Line height.
</dd>
<dt><code>txt</code></dt>
<dd>
String to print.
</dd>
<dt><code>link</code></dt>
<dd>
URL or identifier returned by AddLink().
</dd>
</dl>
<h2>Example</h2>
<div class="doc-source">
<pre><code>// Begin with regular font
$pdf-&gt;SetFont('Arial','',14);
$pdf-&gt;Write(5,'Visit ');
// Then put a blue underlined link
$pdf-&gt;SetTextColor(0,0,255);
$pdf-&gt;SetFont('','U');
$pdf-&gt;Write(5,'www.fpdf.org','http://www.fpdf.org');</code></pre>
</div>
<h2>See also</h2>
<a href="setfont.htm">SetFont</a>,
<a href="settextcolor.htm">SetTextColor</a>,
<a href="addlink.htm">AddLink</a>,
<a href="multicell.htm">MultiCell</a>,
<a href="setautopagebreak.htm">SetAutoPageBreak</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
